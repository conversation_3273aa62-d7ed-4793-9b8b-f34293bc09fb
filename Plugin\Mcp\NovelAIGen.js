// Plugin/Mcp/NovelAIGen.js - NovelAI图片生成MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production' 
        ? '../../utils/logger.cjs' 
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.log(`[插件-${name}] ${msg}`, data || '')
    };
}


class NovelAIGenMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'NovelAIGen';
        this.description = '生成动漫风格图片的专业工具。适用于所有图片生成需求包括角色绘制、风景、物品等。重要规则：1)当用户要求生成特定角色的图片时，将include_character设为true，并在character_names数组中指定角色名称，系统会自动使用对应角色的外貌特征。2)模型选择：luminaArchitect系列适合高质量动漫图片；noobai-fast适合快速生成；kusa-image-generator适合kusa风格图片；当用户要求"审核安全"、"内容审核"或担心内容问题时使用带-free后缀的模型（wai-illustrious-free、anishadow-v10-free）';
        this.vcpName = 'NovelAIGen';
    }

    getParameters() {
        return {
            type: 'object',
            properties: {
                prompt: {
                    type: 'string',
                    description: '图片生成的场景描述，如"在咖啡厅拍照"、"海边拍照"、"画一幅风景画"等。描述想要的场景、动作、背景、氛围等'
                },
                include_character: {
                    type: 'boolean',
                    description: '是否明确需要画角色。当用户要求生成特定角色的图片时设为true，系统将自动使用对应角色的外貌特征。风景或物品图片设为false',
                    default: false
                },
                character_names: {
                    type: 'array',
                    items: {
                        type: 'string'
                    },
                    description: '要画的角色名称数组，按照优先级排列。只有当include_character为true时才需要提供。系统会查找对应的角色外貌配置（格式：角色名_CHARACTER_APPEARANCE）',
                    default: []
                },
                model: {
                    type: 'string',
                    description: '使用的模型。luminaArchitect系列适合高质量动漫风格；noobai-fast适合快速生成；kusa-image-generator适合kusa风格；带-free后缀的模型适合需要内容审核的图片',
                    enum: [
                        'luminaArchitect-v1-fast',
                        'luminaArchitect-v1-turbo',
                        'luminaArchitect-v1-plus',
                        'noobai-fast',
                        'kusa-image-generator',
                        'wai-illustrious-free',
                        'anishadow-v10-free'
                    ],
                    default: 'luminaArchitect-v1-fast'
                }
            },
            required: ['prompt']
        };
    }

    async execute(args) {
        // 验证参数
        this.validateArgs(args);

        // 如果没有提供model，使用默认值
        if (!args.model) {
            args.model = 'luminaArchitect-v1-fast';
        }

        this.log('info', `开始生成NovelAI图片`, {
            prompt: args.prompt,
            include_character: args.include_character || false,
            character_names: args.character_names || [],
            model: args.model
        });
        
        try {
            // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);
            
            // 解析VCP插件返回的结果
            let parsedResult;
            try {
                parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
            } catch (e) {
                parsedResult = result;
            }
            
            // 构建标准响应格式
            const response = {
                type: 'novelai_generation',
                status: 'success',
                message: '图片生成完成',
                data: {
                    prompt: args.prompt,
                    include_character: args.include_character || false,
                    character_names: args.character_names || [],
                    model: args.model,
                    image_url: parsedResult?.image_url,
                    image_path: parsedResult?.image_path,
                    markdown_display: `![${args.prompt.substring(0, 80)}](${parsedResult?.image_url})`,
                    generation_info: {
                        model_used: parsedResult?.model,
                        generation_time: parsedResult?.generation_time,
                        character_included: !!args.include_character,
                        characters_used: args.character_names || []
                    }
                }
            };
        
            this.log('success', `NovelAI图片生成完成`, {
                prompt: args.prompt,
                model: response.data.model,
                image_path: response.data.image_path,
                characters_used: args.character_names || []
            });
        
            return response;
            
        } catch (error) {
            const errorResponse = {
            type: 'novelai_generation',
                status: 'error',
                message: error.message,
                data: {
                    prompt: args.prompt,
                    include_character: args.include_character || false,
                    character_names: args.character_names || [],
                    model: args.model,
                    error: error.message
                }
            };
            
            this.log('error', `NovelAI图片生成失败`, errorResponse);
            throw errorResponse;
        }
    }

    // 重写初始化方法
    async initialize() {
        await super.initialize();
        
        // 检查VCP插件是否可用
        try {
            const pluginManager = global.pluginManager;
            if (!pluginManager.getPlugin(this.vcpName)) {
                throw new Error(`找不到对应的VCP插件: ${this.vcpName}`);
            }
            
            // 检查必要的环境变量
            const requiredEnvVars = ['YUANPLUS_API_KEY'];
            for (const envVar of requiredEnvVars) {
                if (!process.env[envVar]) {
                    throw new Error(`未配置${envVar}环境变量`);
                }
            }
        } catch (error) {
            this.log('error', `插件初始化失败: ${error.message}`);
            throw error;
        }
        
        return true;
    }
}

module.exports = NovelAIGenMcp; 