#!/usr/bin/env node
const axios = require("axios");
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// 引入VCP日志系统
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production'
        ? '../../utils/logger.cjs'
        : '../../utils/logger.js';
    logger = require(loggerPath).default || require(loggerPath);
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}

// Simple UUID generator (替代uuid库)
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

/**
 * 加载插件配置，优先使用插件自己的config.env
 */
function loadPluginConfig() {
    const pluginConfigPath = path.join(__dirname, 'config.env');

    let pluginConfig = {};

    // 加载插件配置
    try {
        const configContent = require('fs').readFileSync(pluginConfigPath, 'utf8');
        pluginConfig = parseEnvConfig(configContent);
        // logger.debug('NovelAIGen', `从插件config.env加载了 ${Object.keys(pluginConfig).length} 个配置项`);
    } catch (error) {
        // logger.debug('NovelAIGen', '插件config.env不存在或读取失败，使用主服务器配置作为备用');
    }

    // 配置优先级：插件配置 > 主服务器环境变量 > 默认值
    return {
        YUANPLUS_API_KEY: pluginConfig.YUANPLUS_API_KEY || process.env.YUANPLUS_API_KEY || '',
        PROJECT_BASE_PATH: pluginConfig.PROJECT_BASE_PATH || process.env.PROJECT_BASE_PATH || '',
        SERVER_PORT: pluginConfig.SERVER_PORT || process.env.SERVER_PORT || '6005',
        IMAGESERVER_IMAGE_KEY: pluginConfig.IMAGESERVER_IMAGE_KEY || process.env.IMAGESERVER_IMAGE_KEY || '',
        VarHttpUrl: pluginConfig.VarHttpUrl || process.env.VarHttpUrl || '',

        // 角色外貌特征相关配置
        ENABLE_CHARACTER_APPEARANCE: pluginConfig.ENABLE_CHARACTER_APPEARANCE ?? true,
        DEFAULT_CHARACTER_APPEARANCE: pluginConfig.DEFAULT_CHARACTER_APPEARANCE || process.env.DEFAULT_CHARACTER_APPEARANCE || 'best quality, ultra-detailed, absurdres, 1girl, anime style',

        // 全局角色特征（从主服务器获取）
        CharAppearance: process.env.CharAppearance || ''
    };
}

/**
 * 解析.env格式的配置文件
 */
function parseEnvConfig(content) {
    const config = {};
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0) {
                const key = line.substring(0, equalIndex).trim();
                const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');

                // 处理布尔值和数字
                if (value.toLowerCase() === 'true') {
                    config[key] = true;
                } else if (value.toLowerCase() === 'false') {
                    config[key] = false;
                } else if (!isNaN(value) && value !== '') {
                    config[key] = parseFloat(value);
                } else {
                    config[key] = value;
                }
            }
        }
    });
    return config;
}

// --- Configuration (优先使用插件配置，主服务器配置作为备用) ---
const config = loadPluginConfig();

const YUANPLUS_API_KEY = config.YUANPLUS_API_KEY;
const PROJECT_BASE_PATH = config.PROJECT_BASE_PATH;
const SERVER_PORT = config.SERVER_PORT;
const IMAGESERVER_IMAGE_KEY = config.IMAGESERVER_IMAGE_KEY;
const VAR_HTTP_URL = config.VarHttpUrl;

// 角色外貌特征相关配置
const ENABLE_CHARACTER_APPEARANCE = config.ENABLE_CHARACTER_APPEARANCE;
const DEFAULT_CHARACTER_APPEARANCE = config.DEFAULT_CHARACTER_APPEARANCE;
const CHAR_APPEARANCE = config.CharAppearance;

// YuanPlus API specific configurations
const YUANPLUS_API_CONFIG = {
    BASE_URL: 'https://yuanplus.cloud/v1/chat/completions',
    DEFAULT_MODEL: 'luminaArchitect-v1-fast',
    AVAILABLE_MODELS: [
        'luminaArchitect-v1-fast',
        'luminaArchitect-v1-turbo',
        'luminaArchitect-v1-plus',
        'noobai-fast',
        'kusa-image-generator',
        'wai-illustrious-free',
        'anishadow-v10-free'
    ]
};

// Helper to validate input arguments
function isValidNovelAIGenArgs(args) {
    if (!args || typeof args !== 'object') return false;
    if (typeof args.prompt !== 'string' || !args.prompt.trim()) return false;
    if (args.model && typeof args.model !== 'string') return false;
    if (args.is_selfie !== undefined && typeof args.is_selfie !== 'boolean') return false;
    return true;
}

/**
 * 获取角色外貌相关的环境变量，按优先级排序
 * @returns {Array<string>} - 角色外貌提示词的数组，按优先级排序
 */
function getCharacterAppearancePrompts() {
    const appearancePrompts = [];
    const allEnvVars = { ...process.env, ...config };

    // 定义优先级顺序（优先级从高到低）
    const priorityOrder = [
        'MAIN_CHARACTER_APPEARANCE',
        'PRIMARY_CHARACTER_APPEARANCE',
        'DEFAULT_CHARACTER_APPEARANCE',
        'SECONDARY_CHARACTER_APPEARANCE',
        'CASUAL_CHARACTER_APPEARANCE',
        'FORMAL_CHARACTER_APPEARANCE'
    ];

    // 首先按优先级顺序添加已知的配置
    for (const key of priorityOrder) {
        const value = allEnvVars[key];
        if (value && typeof value === 'string' && value.trim()) {
            appearancePrompts.push(value.trim());
            logger.debug('NovelAIGen', `发现优先级角色外貌配置: ${key} = ${value.trim()}`);
        }
    }

    // 然后添加其他未在优先级列表中的 _CHARACTER_APPEARANCE 变量
    for (const [key, value] of Object.entries(allEnvVars)) {
        if (key.endsWith('_CHARACTER_APPEARANCE') &&
            !priorityOrder.includes(key) &&
            value && typeof value === 'string' && value.trim()) {
            appearancePrompts.push(value.trim());
            logger.debug('NovelAIGen', `发现其他角色外貌配置: ${key} = ${value.trim()}`);
        }
    }

    // 如果没有找到任何 xxxx_CHARACTER_APPEARANCE 变量，使用传统配置
    if (appearancePrompts.length === 0) {
        if (CHAR_APPEARANCE && CHAR_APPEARANCE.trim()) {
            appearancePrompts.push(CHAR_APPEARANCE.trim());
            logger.debug('NovelAIGen', `使用传统全局配置: CharAppearance = ${CHAR_APPEARANCE.trim()}`);
        } else if (DEFAULT_CHARACTER_APPEARANCE && DEFAULT_CHARACTER_APPEARANCE.trim()) {
            appearancePrompts.push(DEFAULT_CHARACTER_APPEARANCE.trim());
            logger.debug('NovelAIGen', `使用传统默认配置: DEFAULT_CHARACTER_APPEARANCE = ${DEFAULT_CHARACTER_APPEARANCE.trim()}`);
        }
    }

    return appearancePrompts;
}

/**
 * 处理自拍场景的提示词
 * @param {string} scenePrompt - 场景描述（如"在咖啡厅自拍"）
 * @param {boolean} isSelfie - 是否是自拍/自己的照片
 * @returns {string} - 处理后的完整提示词
 */
function processCharacterAppearancePrompt(scenePrompt, isSelfie) {
    if (!ENABLE_CHARACTER_APPEARANCE || !isSelfie) {
        return scenePrompt;
    }

    // 获取所有角色外貌提示词
    const appearancePrompts = getCharacterAppearancePrompts();

    // 如果有角色外貌特征，则智能合并到场景描述中
    if (appearancePrompts.length > 0) {
        // 限制使用的外貌特征数量，避免提示词过长
        const maxPrompts = 2; // 最多使用前2个优先级最高的外貌特征
        const selectedPrompts = appearancePrompts.slice(0, maxPrompts);

        const combinedAppearance = selectedPrompts.join(', ');
        logger.info('NovelAIGen', `应用角色外貌特征 (${selectedPrompts.length}/${appearancePrompts.length}): ${combinedAppearance}`);

        if (appearancePrompts.length > maxPrompts) {
            logger.debug('NovelAIGen', `跳过了 ${appearancePrompts.length - maxPrompts} 个低优先级外貌特征以避免提示词过长`);
        }

        return `${combinedAppearance}, ${scenePrompt}`;
    }

    // 否则直接返回场景描述
    return scenePrompt;
}

async function YTOtherModels(messages, model) {
    try {
        const data = {
            model,
            messages
        };

        const response = await axios.post(YUANPLUS_API_CONFIG.BASE_URL, data, {
            headers: {
                'Authorization': `Bearer ${YUANPLUS_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: 60000
        });

        if (response.data.error) {
            console.error(response.data);
            return response.data.error;
        }

        return response.data?.choices?.[0]?.message?.content;
    } catch (error) {
        console.error(error);
        return null;
    }
}

async function generateImageAndSave(args) {
    // Check for essential environment variables
    if (!YUANPLUS_API_KEY) {
        throw new Error("NovelAIGen 插件错误：需要设置 YUANPLUS_API_KEY 环境变量。");
    }
    if (!PROJECT_BASE_PATH) {
        throw new Error("NovelAIGen 插件错误：需要设置 PROJECT_BASE_PATH 环境变量以保存图片。");
    }
    if (!SERVER_PORT) {
        throw new Error("NovelAIGen 插件错误：需要设置 SERVER_PORT 环境变量以构建图片URL。");
    }
    if (!IMAGESERVER_IMAGE_KEY) {
        throw new Error("NovelAIGen 插件错误：需要设置 IMAGESERVER_IMAGE_KEY 环境变量以构建图片URL。");
    }
    if (!VAR_HTTP_URL) {
        throw new Error("NovelAIGen 插件错误：需要设置 VarHttpUrl 环境变量以构建图片URL。");
    }

    if (!isValidNovelAIGenArgs(args)) {
        throw new Error(`NovelAIGen 插件错误：收到无效参数: ${JSON.stringify(args)}。必需参数: prompt (字符串)。可选参数: model (字符串), is_selfie (布尔值)。`);
    }

    const model = args.model || YUANPLUS_API_CONFIG.DEFAULT_MODEL;

    // 处理自拍场景的提示词
    const processedPrompt = processCharacterAppearancePrompt(args.prompt, args.is_selfie);
    const messages = [{ role: "user", content: processedPrompt }];

    // 记录提示词处理信息
    if (processedPrompt !== args.prompt) {
        // logger.info('NovelAIGen', `提示词已处理: "${args.prompt}" -> "${processedPrompt}"`);
    }

    // Call the image generation API
    const imageUrls = await YTOtherModels(messages, model);

    if (!imageUrls) {
        throw new Error("NovelAIGen 插件错误：无法从 YuanPlus API 获取图片URL。");
    }

    // Parse image URLs (assuming the API returns URLs in the response)
    let imageUrl;
    try {
        // Try to parse as JSON in case the response contains structured data
        const parsed = JSON.parse(imageUrls);
        imageUrl = parsed.image_url || parsed.url || imageUrls;
    } catch (e) {
        // If not JSON, treat as direct URL or search for URL patterns
        const urlMatch = imageUrls.match(/https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp)/i);
        imageUrl = urlMatch ? urlMatch[0] : imageUrls;
    }

    if (!imageUrl || (!imageUrl.startsWith('http') && !imageUrl.startsWith('data:'))) {
        throw new Error("NovelAIGen 插件错误：无法从响应中提取有效的图片URL。");
    }

    let imageData;
    let imageExtension = 'png';

    if (imageUrl.startsWith('data:')) {
        // Handle base64 data URLs
        const matches = imageUrl.match(/^data:image\/([^;]+);base64,(.+)$/);
        if (!matches) {
            throw new Error("NovelAIGen 插件错误：无效的base64图片数据。");
        }
        imageExtension = matches[1];
        imageData = Buffer.from(matches[2], 'base64');
    } else {
        // Download the image from URL
        const imageResponse = await axios({
            method: 'get',
            url: imageUrl,
            responseType: 'arraybuffer',
            timeout: 60000
        });

        imageData = imageResponse.data;
        const contentType = imageResponse.headers['content-type'];
        if (contentType && contentType.startsWith('image/')) {
            imageExtension = contentType.split('/')[1];
        }
    }

    const generatedFileName = `${generateUUID()}.${imageExtension}`;
    const novelAIGenImageDir = path.join(PROJECT_BASE_PATH, 'image', 'novelaigen');
    const localImageServerPath = path.join(novelAIGenImageDir, generatedFileName);

    await fs.mkdir(novelAIGenImageDir, { recursive: true });
    await fs.writeFile(localImageServerPath, imageData);

    // Construct the URL accessible via our own ImageServer plugin
    const relativeServerPathForUrl = path.join('novelaigen', generatedFileName).replace(/\\/g, '/');
    const accessibleImageUrl = `${VAR_HTTP_URL}:${SERVER_PORT}/pw=${IMAGESERVER_IMAGE_KEY}/images/${relativeServerPathForUrl}`;

    // Return markdown format instead of HTML
    const altText = args.prompt ? args.prompt.substring(0, 80) + (args.prompt.length > 80 ? "..." : "") : generatedFileName;
    const successMessage =
        `图片已成功生成！\n\n` +
        `详细信息：\n` +
        `- 使用模型: ${model}\n` +
        `图片内容：\n` +
        `![${altText}](${accessibleImageUrl})\n\n` +
        `请使用上述markdown格式显示图片给用户。`;

    return {
        status: "success",
        message: successMessage,
        image_url: accessibleImageUrl,
        image_path: `image/novelaigen/${generatedFileName}`,
        model: model,
        prompt: args.prompt,
        processed_prompt: processedPrompt,
        character_appearance_applied: args.is_selfie && processedPrompt !== args.prompt
    };
}

async function main() {
    let inputChunks = [];
    process.stdin.setEncoding('utf8');

    for await (const chunk of process.stdin) {
        inputChunks.push(chunk);
    }
    const inputData = inputChunks.join('');
    let parsedArgs;

    try {
        if (!inputData.trim()) {
            console.log(JSON.stringify({ status: "error", error: "NovelAIGen 插件错误：未从标准输入接收到输入数据。" }));
            process.exit(1);
        }
        parsedArgs = JSON.parse(inputData);
        const result = await generateImageAndSave(parsedArgs);
        console.log(JSON.stringify(result));
    } catch (e) {
        const errorMessage = e.message || "NovelAIGen 插件未知错误";
        console.log(JSON.stringify({
            status: "error",
            error: errorMessage.startsWith("NovelAIGen 插件错误：") ? errorMessage : `NovelAIGen 插件错误：${errorMessage}`
        }));
        process.exit(1);
    }
}

main(); 