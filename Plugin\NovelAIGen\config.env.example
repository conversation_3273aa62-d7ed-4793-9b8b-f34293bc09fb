YUANPLUS_API_KEY=your_yuanplus_api_key_here

# 角色外貌特征配置示例
# 系统会自动检测所有以 "_CHARACTER_APPEARANCE" 结尾的环境变量
# 当用户生成自拍/自己的照片时，这些特征会自动添加到提示词中
#
# 优先级顺序（高到低）：
# 1. MAIN_CHARACTER_APPEARANCE (主角色外貌，最高优先级)
# 2. PRIMARY_CHARACTER_APPEARANCE (主要角色外貌)
# 3. DEFAULT_CHARACTER_APPEARANCE (默认角色外貌)
# 4. SECONDARY_CHARACTER_APPEARANCE (次要角色外貌)
# 5. CASUAL_CHARACTER_APPEARANCE (休闲装扮)
# 6. FORMAL_CHARACTER_APPEARANCE (正式装扮)
# 7. 其他自定义的 xxxx_CHARACTER_APPEARANCE 变量
#
# 注意：为避免提示词过长，系统最多使用前2个优先级最高的外貌特征

# 主角色外貌特征（最高优先级，强烈推荐设置）
MAIN_CHARACTER_APPEARANCE=1girl, long black hair, brown eyes, anime style, cute face

# 备用角色外貌特征（可选）
# PRIMARY_CHARACTER_APPEARANCE=best quality, ultra-detailed, absurdres, 1girl, anime style
# SECONDARY_CHARACTER_APPEARANCE=1boy, short brown hair, blue eyes, handsome

# 特定场景角色外貌（可选，优先级较低）
# CASUAL_CHARACTER_APPEARANCE=casual clothes, t-shirt, jeans
# FORMAL_CHARACTER_APPEARANCE=formal dress, elegant style

# 自定义角色外貌（可以根据需要添加任意数量的配置）
# SCHOOL_CHARACTER_APPEARANCE=school uniform, student, youthful
# WORK_CHARACTER_APPEARANCE=business suit, professional, mature

# 传统配置（向后兼容，当没有任何 xxxx_CHARACTER_APPEARANCE 配置时使用）
# DEFAULT_CHARACTER_APPEARANCE=best quality, ultra-detailed, absurdres, 1girl, anime style
# ENABLE_CHARACTER_APPEARANCE=true