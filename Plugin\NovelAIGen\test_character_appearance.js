#!/usr/bin/env node

/**
 * 测试角色外貌环境变量检测功能
 * 用于验证 xxxx_CHARACTER_APPEARANCE 格式的环境变量是否能正确被检测和应用
 */

// 模拟环境变量
process.env.MAIN_CHARACTER_APPEARANCE = "1girl, long black hair, brown eyes, anime style, cute face";
process.env.PRIMARY_CHARACTER_APPEARANCE = "best quality, ultra-detailed, absurdres";
process.env.CASUAL_CHARACTER_APPEARANCE = "casual clothes, t-shirt, jeans";
process.env.FORMAL_CHARACTER_APPEARANCE = "formal dress, elegant style";
process.env.SECONDARY_CHARACTER_APPEARANCE = "1boy, short brown hair, blue eyes, handsome";
process.env.SCHOOL_CHARACTER_APPEARANCE = "school uniform, student, youthful";
process.env.CUSTOM_CHARACTER_APPEARANCE = "custom style, unique design";

// 模拟一些非相关的环境变量
process.env.API_KEY = "test_key";
process.env.SOME_OTHER_CONFIG = "other_value";
process.env.CHARACTER_NAME = "test_character"; // 不以 _CHARACTER_APPEARANCE 结尾

// 引入插件配置加载函数
const path = require('path');

// 模拟 loadPluginConfig 函数
function loadPluginConfig() {
    const pluginConfigPath = path.join(__dirname, 'config.env');
    let pluginConfig = {};

    // 加载插件配置
    try {
        const configContent = require('fs').readFileSync(pluginConfigPath, 'utf8');
        pluginConfig = parseEnvConfig(configContent);
        console.log(`从插件config.env加载了 ${Object.keys(pluginConfig).length} 个配置项`);
    } catch (error) {
        console.log('插件config.env不存在或读取失败，使用环境变量');
    }

    // 配置优先级：插件配置 > 主服务器环境变量 > 默认值
    return {
        YUANPLUS_API_KEY: pluginConfig.YUANPLUS_API_KEY || process.env.YUANPLUS_API_KEY || '',
        PROJECT_BASE_PATH: pluginConfig.PROJECT_BASE_PATH || process.env.PROJECT_BASE_PATH || '',
        SERVER_PORT: pluginConfig.SERVER_PORT || process.env.SERVER_PORT || '6005',
        IMAGESERVER_IMAGE_KEY: pluginConfig.IMAGESERVER_IMAGE_KEY || process.env.IMAGESERVER_IMAGE_KEY || '',
        VarHttpUrl: pluginConfig.VarHttpUrl || process.env.VarHttpUrl || '',

        // 角色外貌特征相关配置
        ENABLE_CHARACTER_APPEARANCE: pluginConfig.ENABLE_CHARACTER_APPEARANCE ?? true,
        DEFAULT_CHARACTER_APPEARANCE: pluginConfig.DEFAULT_CHARACTER_APPEARANCE || process.env.DEFAULT_CHARACTER_APPEARANCE || 'best quality, ultra-detailed, absurdres, 1girl, anime style',

        // 全局角色特征（从主服务器获取）
        CharAppearance: process.env.CharAppearance || ''
    };
}

function parseEnvConfig(content) {
    const config = {};
    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0) {
                const key = line.substring(0, equalIndex).trim();
                const value = line.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');

                // 处理布尔值和数字
                if (value.toLowerCase() === 'true') {
                    config[key] = true;
                } else if (value.toLowerCase() === 'false') {
                    config[key] = false;
                } else if (!isNaN(value) && value !== '') {
                    config[key] = parseFloat(value);
                } else {
                    config[key] = value;
                }
            }
        }
    });
    return config;
}

// 模拟日志系统
const logger = {
    debug: (component, msg, data) => console.log(`[DEBUG-${component}] ${msg}`, data || ''),
    info: (component, msg, data) => console.log(`[INFO-${component}] ${msg}`, data || ''),
    error: (component, msg, data) => console.error(`[ERROR-${component}] ${msg}`, data || ''),
    warning: (component, msg, data) => console.warn(`[WARNING-${component}] ${msg}`, data || '')
};

// 加载配置
const config = loadPluginConfig();

// 测试角色外貌检测函数（与主插件保持一致）
function getCharacterAppearancePrompts() {
    const appearancePrompts = [];
    const allEnvVars = { ...process.env, ...config };

    // 定义优先级顺序（优先级从高到低）
    const priorityOrder = [
        'MAIN_CHARACTER_APPEARANCE',
        'PRIMARY_CHARACTER_APPEARANCE',
        'DEFAULT_CHARACTER_APPEARANCE',
        'SECONDARY_CHARACTER_APPEARANCE',
        'CASUAL_CHARACTER_APPEARANCE',
        'FORMAL_CHARACTER_APPEARANCE'
    ];

    // 首先按优先级顺序添加已知的配置
    for (const key of priorityOrder) {
        const value = allEnvVars[key];
        if (value && typeof value === 'string' && value.trim()) {
            appearancePrompts.push(value.trim());
            logger.debug('NovelAIGen', `发现优先级角色外貌配置: ${key} = ${value.trim()}`);
        }
    }

    // 然后添加其他未在优先级列表中的 _CHARACTER_APPEARANCE 变量
    for (const [key, value] of Object.entries(allEnvVars)) {
        if (key.endsWith('_CHARACTER_APPEARANCE') &&
            !priorityOrder.includes(key) &&
            value && typeof value === 'string' && value.trim()) {
            appearancePrompts.push(value.trim());
            logger.debug('NovelAIGen', `发现其他角色外貌配置: ${key} = ${value.trim()}`);
        }
    }

    // 如果没有找到任何 xxxx_CHARACTER_APPEARANCE 变量，使用传统配置
    if (appearancePrompts.length === 0) {
        const CHAR_APPEARANCE = config.CharAppearance;
        const DEFAULT_CHARACTER_APPEARANCE = config.DEFAULT_CHARACTER_APPEARANCE;

        if (CHAR_APPEARANCE && CHAR_APPEARANCE.trim()) {
            appearancePrompts.push(CHAR_APPEARANCE.trim());
            logger.debug('NovelAIGen', `使用传统全局配置: CharAppearance = ${CHAR_APPEARANCE.trim()}`);
        } else if (DEFAULT_CHARACTER_APPEARANCE && DEFAULT_CHARACTER_APPEARANCE.trim()) {
            appearancePrompts.push(DEFAULT_CHARACTER_APPEARANCE.trim());
            logger.debug('NovelAIGen', `使用传统默认配置: DEFAULT_CHARACTER_APPEARANCE = ${DEFAULT_CHARACTER_APPEARANCE.trim()}`);
        }
    }

    return appearancePrompts;
}

function processCharacterAppearancePrompt(scenePrompt, isSelfie) {
    const ENABLE_CHARACTER_APPEARANCE = config.ENABLE_CHARACTER_APPEARANCE;

    if (!ENABLE_CHARACTER_APPEARANCE || !isSelfie) {
        return scenePrompt;
    }

    // 获取所有角色外貌提示词
    const appearancePrompts = getCharacterAppearancePrompts();

    // 如果有角色外貌特征，则智能合并到场景描述中
    if (appearancePrompts.length > 0) {
        // 限制使用的外貌特征数量，避免提示词过长
        const maxPrompts = 2; // 最多使用前2个优先级最高的外貌特征
        const selectedPrompts = appearancePrompts.slice(0, maxPrompts);

        const combinedAppearance = selectedPrompts.join(', ');
        logger.info('NovelAIGen', `应用角色外貌特征 (${selectedPrompts.length}/${appearancePrompts.length}): ${combinedAppearance}`);

        if (appearancePrompts.length > maxPrompts) {
            logger.debug('NovelAIGen', `跳过了 ${appearancePrompts.length - maxPrompts} 个低优先级外貌特征以避免提示词过长`);
        }

        return `${combinedAppearance}, ${scenePrompt}`;
    }

    // 否则直接返回场景描述
    return scenePrompt;
}

// 运行测试
console.log('=== NovelAI 角色外貌环境变量检测测试 ===\n');

console.log('1. 测试环境变量检测:');
const appearancePrompts = getCharacterAppearancePrompts();
console.log(`检测到 ${appearancePrompts.length} 个角色外貌配置:`);
appearancePrompts.forEach((prompt, index) => {
    console.log(`  ${index + 1}. ${prompt}`);
});

console.log('\n2. 测试提示词处理 (自拍场景):');
const testPrompt = "在咖啡厅自拍";
const processedPrompt = processCharacterAppearancePrompt(testPrompt, true);
console.log(`原始提示词: ${testPrompt}`);
console.log(`处理后提示词: ${processedPrompt}`);

console.log('\n3. 测试提示词处理 (非自拍场景):');
const testPrompt2 = "画一幅风景画";
const processedPrompt2 = processCharacterAppearancePrompt(testPrompt2, false);
console.log(`原始提示词: ${testPrompt2}`);
console.log(`处理后提示词: ${processedPrompt2}`);

console.log('\n=== 测试完成 ===');
