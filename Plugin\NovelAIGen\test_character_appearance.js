#!/usr/bin/env node

/**
 * NovelAI角色外貌配置功能测试脚本
 * 用于测试新的角色外貌配置功能
 */

const path = require('path');
const fs = require('fs');

// 模拟环境变量和配置
process.env.ALICE_CHARACTER_APPEARANCE = '1girl, blonde hair, blue eyes, school uniform, cute smile';
process.env.BOB_CHARACTER_APPEARANCE = '1boy, brown hair, green eyes, casual clothes, friendly expression';
process.env.MIKU_CHARACTER_APPEARANCE = '1girl, long turquoise twin tails, blue eyes, futuristic outfit, vocaloid';

// 创建测试配置文件
const testConfigContent = `
ENABLE_CHARACTER_APPEARANCE=true
DEFAULT_CHARACTER_APPEARANCE=best quality, ultra-detailed, absurdres, anime style
SAKURA_CHARACTER_APPEARANCE=1girl, pink hair, green eyes, magical girl outfit, cherry blossoms
UNKNOWN_CHARACTER_APPEARANCE=1girl, mysterious appearance, dark clothes
`;

const testConfigPath = path.join(__dirname, 'test_config.env');
fs.writeFileSync(testConfigPath, testConfigContent);

// 临时修改配置路径进行测试
const originalConfigPath = path.join(__dirname, 'config.env');
const backupConfigPath = path.join(__dirname, 'config.env.backup');

// 备份原配置
if (fs.existsSync(originalConfigPath)) {
    fs.copyFileSync(originalConfigPath, backupConfigPath);
}

// 使用测试配置
fs.copyFileSync(testConfigPath, originalConfigPath);

try {
    // 重新加载模块以使用新配置
    delete require.cache[require.resolve('./NovelAIGen.js')];
    
    console.log('=== NovelAI角色外貌配置功能测试 ===\n');
    
    // 测试用例
    const testCases = [
        {
            name: '单个角色测试 - ALICE',
            args: {
                prompt: '在咖啡厅拍照',
                include_character: true,
                character_names: ['ALICE'],
                model: 'luminaArchitect-v1-fast'
            }
        },
        {
            name: '多个角色测试 - ALICE和BOB',
            args: {
                prompt: '在公园里合影',
                include_character: true,
                character_names: ['ALICE', 'BOB'],
                model: 'luminaArchitect-v1-fast'
            }
        },
        {
            name: '插件配置角色测试 - SAKURA',
            args: {
                prompt: '在樱花树下拍照',
                include_character: true,
                character_names: ['SAKURA'],
                model: 'luminaArchitect-v1-fast'
            }
        },
        {
            name: '不存在的角色测试 - 使用默认配置',
            args: {
                prompt: '在海边拍照',
                include_character: true,
                character_names: ['NONEXISTENT'],
                model: 'luminaArchitect-v1-fast'
            }
        },
        {
            name: '不包含角色测试 - 纯风景',
            args: {
                prompt: '美丽的山景',
                include_character: false,
                character_names: [],
                model: 'luminaArchitect-v1-fast'
            }
        },
        {
            name: '空角色名称数组测试',
            args: {
                prompt: '在教室里拍照',
                include_character: true,
                character_names: [],
                model: 'luminaArchitect-v1-fast'
            }
        }
    ];
    
    // 模拟processCharacterAppearancePrompt函数进行测试
    function testProcessCharacterAppearancePrompt() {
        // 模拟配置加载
        const mockConfig = {
            ENABLE_CHARACTER_APPEARANCE: true,
            DEFAULT_CHARACTER_APPEARANCE: 'best quality, ultra-detailed, absurdres, anime style',
            characterAppearances: {
                'ALICE': '1girl, blonde hair, blue eyes, school uniform, cute smile',
                'BOB': '1boy, brown hair, green eyes, casual clothes, friendly expression',
                'MIKU': '1girl, long turquoise twin tails, blue eyes, futuristic outfit, vocaloid',
                'SAKURA': '1girl, pink hair, green eyes, magical girl outfit, cherry blossoms'
            }
        };
        
        function processCharacterAppearancePrompt(scenePrompt, includeCharacter, characterNames = []) {
            if (!mockConfig.ENABLE_CHARACTER_APPEARANCE || !includeCharacter) {
                return scenePrompt;
            }
        
            let characterAppearances = [];
        
            // 遍历角色名称，查找对应的外貌配置
            if (characterNames && characterNames.length > 0) {
                for (const characterName of characterNames) {
                    if (mockConfig.characterAppearances[characterName]) {
                        characterAppearances.push(mockConfig.characterAppearances[characterName].trim());
                    }
                }
            }
        
            // 如果没有找到特定角色的外貌配置，使用默认配置
            if (characterAppearances.length === 0) {
                if (mockConfig.DEFAULT_CHARACTER_APPEARANCE && mockConfig.DEFAULT_CHARACTER_APPEARANCE.trim()) {
                    characterAppearances.push(mockConfig.DEFAULT_CHARACTER_APPEARANCE.trim());
                }
            }
        
            // 如果有角色外貌特征，则合并到场景描述中
            if (characterAppearances.length > 0) {
                const combinedAppearances = characterAppearances.join(', ');
                return `${combinedAppearances}, ${scenePrompt}`;
            }
        
            // 否则直接返回场景描述
            return scenePrompt;
        }
        
        // 执行测试用例
        testCases.forEach((testCase, index) => {
            console.log(`${index + 1}. ${testCase.name}`);
            console.log(`   输入提示词: "${testCase.args.prompt}"`);
            console.log(`   包含角色: ${testCase.args.include_character}`);
            console.log(`   角色名称: [${testCase.args.character_names.join(', ')}]`);
            
            const processedPrompt = processCharacterAppearancePrompt(
                testCase.args.prompt,
                testCase.args.include_character,
                testCase.args.character_names
            );
            
            console.log(`   处理后提示词: "${processedPrompt}"`);
            console.log(`   是否修改: ${processedPrompt !== testCase.args.prompt ? '是' : '否'}`);
            console.log('');
        });
    }
    
    testProcessCharacterAppearancePrompt();
    
    console.log('=== 测试完成 ===');
    console.log('所有测试用例已执行完毕。');
    console.log('\n使用说明：');
    console.log('1. 在config.env中添加角色外貌配置，格式：角色名_CHARACTER_APPEARANCE=外貌描述');
    console.log('2. 调用插件时设置include_character=true和character_names数组');
    console.log('3. 系统会自动查找对应角色的外貌配置并添加到提示词中');
    
} finally {
    // 恢复原配置
    if (fs.existsSync(backupConfigPath)) {
        fs.copyFileSync(backupConfigPath, originalConfigPath);
        fs.unlinkSync(backupConfigPath);
    } else {
        if (fs.existsSync(originalConfigPath)) {
            fs.unlinkSync(originalConfigPath);
        }
    }
    
    // 清理测试文件
    if (fs.existsSync(testConfigPath)) {
        fs.unlinkSync(testConfigPath);
    }
}
